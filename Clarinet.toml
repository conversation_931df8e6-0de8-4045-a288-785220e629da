[project]
name = 'bookchain-library'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.access-control]
path = 'contracts/access-control.clar'
clarity_version = 3
epoch = 3.1

[contracts.admin]
path = 'contracts/admin.clar'
clarity_version = 3
epoch = 3.1

[contracts.book-nft]
path = 'contracts/book-nft.clar'
clarity_version = 3
epoch = 3.1

[contracts.borrow-agreement]
path = 'contracts/borrow-agreement.clar'
clarity_version = 3
epoch = 3.1

[contracts.lending-pool]
path = 'contracts/lending-pool.clar'
clarity_version = 3
epoch = 3.1

[contracts.penalty]
path = 'contracts/penalty.clar'
clarity_version = 3
epoch = 3.1

[contracts.reputation]
path = 'contracts/reputation.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
